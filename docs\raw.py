
import torch
import torch.nn as nn

class SpikingSSM(nn.Module):
	def __init__(self, d, m, vocab_size, tau=0.9, theta=1.0, epsilon=0.05, sigma=0.1, k=32, X=5, pad_token=0):
		super(SpikingSSM, self).__init__()
		self.d = d  # Embedding dimension
		self.m = m  # State size
		self.vocab_size = vocab_size
		self.tau = tau  # Leak rate
		self.theta = torch.tensor(theta)  # Threshold
		self.epsilon = epsilon  # Noise scale
		self.sigma = sigma  # Noise variance
		self.k = k  # Pattern dimension
		self.X = X  # Number of tokens to predict at once
		self.pad_token = pad_token

		# SSM parameters
		self.A_bar = nn.Parameter(torch.randn(m, m))
		self.B_bar = nn.Parameter(torch.randn(m, d))
		self.C = nn.Parameter(torch.randn(d, m))

		# Neuromodulation (example, time-invariant for simplicity)
		self.G = nn.Parameter(torch.ones(d))

		# Pattern prediction projection
		self.W_p = nn.Parameter(torch.randn(k, m))
		self.b_p = nn.Parameter(torch.zeros(k))

		# Output projection for multi-token prediction
		# Project spikes to a space for X tokens: d -> X * vocab_size
		self.W_o = nn.Parameter(torch.randn(vocab_size * X, d))
		self.b_o = nn.Parameter(torch.zeros(vocab_size * X))

		# Plasticity mask (initially full, for future growth)
		self.P = torch.ones_like(self.W_o)  # Example for output

	def heaviside(self, x):
		return (x > 0).float()

	def forward(self, X_in):
		# X_in: [batch, n, d] embedded input sequence
		batch, n, _ = X_in.shape
		device = X_in.device

		# Initialize states
		h = torch.zeros(batch, self.m, device=device)
		u = torch.zeros(batch, self.d, device=device)
		outputs = []

		for t in range(n):
			x_t = X_in[:, t, :]

			# SSM state update
			h = torch.matmul(h, self.A_bar) + torch.matmul(x_t, self.B_bar.t())  # Adjusted for shapes

			# Input current with neuromodulation and noise
			I_t = torch.matmul(h, self.C.t()) * self.G  # [batch, d]
			noise = self.epsilon * torch.randn_like(I_t) * self.sigma
			I_prime_t = I_t + noise

			# Potential update
			u_prime = self.tau * u + I_prime_t

			# Spike generation
			s_t = self.heaviside(u_prime - self.theta.to(device))

			# Reset
			u = u_prime * (1 - s_t)

			# Pattern prediction (for internal use, not output)
			p_hat_t = torch.matmul(h, self.W_p.t()) + self.b_p

			# Collect spikes for output
			outputs.append(s_t)

		# Aggregate spikes over time (e.g., mean for simplicity, bio-inspired pooling)
		s_agg = torch.mean(torch.stack(outputs, dim=1), dim=1)  # [batch, d]

		# Multi-token output: project to [batch, X * vocab_size], apply mask
		y = torch.matmul(s_agg, (self.W_o * self.P.to(device)).t()) + self.b_o  # [batch, X*vocab]
		y = y.view(batch, self.X, self.vocab_size)

		# Return logits; sampling/argmax can pad with pad_token if needed externally
		return y

class MultiLayerSpikingSSM(nn.Module):

	def __init__(self, L, d, m, vocab_size):  # Include prior params
		super().__init__()
		self.layers = nn.ModuleList([SpikingSSM(d, m, vocab_size if l == L-1 else d, ...) for l in range(L)])
		# Layer-specific adjustments, e.g., varying epsilon^l

	def forward(self, X_in):
		current_input = X_in  # [batch, n, d]
		for layer in self.layers[:-1]:
			spikes = layer(current_input)  # Intermediate layers output spikes reshaped to [batch, n, d]
			current_input = spikes.mean(dim=1).unsqueeze(1).repeat(1, current_input.size(1), 1)  # Aggregate and broadcast
		final_output = self.layers[-1](current_input)  # [batch, X, vocab_size]
		return final_output
