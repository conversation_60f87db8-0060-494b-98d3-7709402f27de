
import torch
import torch.nn as nn
import torch.nn.functional as F

class SpikingSSM(nn.Module):
	def __init__(self, input_dim, d, m, output_dim, tau=0.9, theta=1.0, epsilon=0.05, sigma=0.1, k=32, X=5):
		super(SpikingSSM, self).__init__()
		self.d = d
		self.m = m
		self.tau = tau
		self.theta = torch.tensor(theta)
		self.epsilon = epsilon
		self.sigma = sigma
		self.k = k
		self.X = X

		# SSM parameters
		self.A_bar = nn.Parameter(torch.randn(m, m))
		self.B_bar = nn.Parameter(torch.randn(m, input_dim))
		self.C = nn.Parameter(torch.randn(d, m))

		# Neuromodulation
		self.G = nn.Parameter(torch.ones(d))

		# Pattern prediction
		self.W_p = nn.Parameter(torch.randn(k, m))
		self.b_p = nn.Parameter(torch.zeros(k))

		# Output projection
		self.W_o = nn.Parameter(torch.randn(output_dim * X, d))
		self.b_o = nn.Parameter(torch.zeros(output_dim * X))

		# Plasticity mask
		self.P = torch.ones_like(self.W_o)

	def heaviside(self, x):
		return (x > 0).float()

	def forward(self, X_in):
		batch, n, input_dim = X_in.shape
		device = X_in.device

		h = torch.zeros(batch, self.m, device=device)
		u = torch.zeros(batch, self.d, device=device)
		outputs = []

		for t in range(n):
			x_t = X_in[:, t, :]

			h = torch.matmul(h, self.A_bar) + torch.matmul(x_t, self.B_bar.t())

			I_t = torch.matmul(h, self.C.t()) * self.G
			noise = self.epsilon * torch.randn_like(I_t) * self.sigma
			I_prime_t = I_t + noise

			u_prime = self.tau * u + I_prime_t
			s_t = self.heaviside(u_prime - self.theta.to(device))
			u = u_prime * (1 - s_t)

			p_hat_t = torch.matmul(h, self.W_p.t()) + self.b_p  # Internal pattern

			outputs.append(s_t)

		s_agg = torch.mean(torch.stack(outputs, dim=1), dim=1)
		y = torch.matmul(s_agg, (self.W_o * self.P.to(device)).t()) + self.b_o
		y = y.view(batch, self.X, -1)  # [batch, X, output_dim]
		return y

class MultiLayerSpikingSSM(nn.Module):
	def __init__(self, L, d, m, vocab_size, X=5):
		super(MultiLayerSpikingSSM, self).__init__()
		self.layers = nn.ModuleList()
		for l in range(L):
			input_dim = d if l > 0 else d  # Consistent dim
			output_dim = d if l < L-1 else vocab_size
			self.layers.append(SpikingSSM(input_dim, d, m, output_dim, X=X if l == L-1 else 1))

	def forward(self, X_in):
		current_input = X_in
		for layer in self.layers:
			y = layer(current_input)
			if layer != self.layers[-1]:
				# Aggregate and reshape spikes for next layer input [batch, n, d]
				current_input = y.mean(dim=1).unsqueeze(1).repeat(1, current_input.size(1), 1)
		return y  # Final [batch, X, vocab_size]

class SpikingSSM_LLM(nn.Module):
	def __init__(self, vocab_size, d=128, m=64, L=4, X=5):
		super(SpikingSSM_LLM, self).__init__()
		self.embedding = nn.Embedding(vocab_size, d)
		self.core = MultiLayerSpikingSSM(L, d, m, vocab_size, X=X)
		self.vocab_size = vocab_size
		self.X = X

	def forward(self, tokens):
		# tokens: [batch, n]
		embeds = self.embedding(tokens)  # [batch, n, d]
		logits = self.core(embeds)  # [batch, X, vocab_size]
		return logits

	def generate(self, tokens, max_new_tokens, temperature=1.0, top_k=None):
		# Autoregressive generation
		device = next(self.parameters()).device
		generated = tokens.clone()
		for _ in range(max_new_tokens):
			embeds = self.embedding(generated[:, -self.X:]) if generated.size(1) >= self.X else self.embedding(generated)
			logits = self.core(embeds)  # [batch, X, vocab_size]
			logits = logits[:, -1, :] / temperature  # Last token prediction

			if top_k is not None:
				v, _ = torch.topk(logits, top_k)
				logits[logits < v[:, [-1]]] = -float('Inf')

			probs = F.softmax(logits, dim=-1)
			next_token = torch.multinomial(probs, num_samples=1)
			generated = torch.cat((generated, next_token), dim=1)
		return generated
