import time
import torch
import torch.nn as nn
import torch.nn.functional as F
import re

from torch.utils.data import Dataset, DataLoader
from datasets import load_dataset
from collections import Counter

# For mixed precision training
from torch.amp import GradScaler

# Custom autograd for surrogate gradient
class SurrogateHeaviside(torch.autograd.Function):
	@staticmethod
	def forward(ctx, input, theta):
		output = (input > theta).float()
		ctx.save_for_backward(input, theta)
		return output

	@staticmethod
	def backward(ctx, grad_output):
		input, theta = ctx.saved_tensors
		grad_input = grad_output.clone()
		surrogate = 1 / (1 + torch.abs(input - theta)) ** 2
		return grad_input * surrogate, None

surrogate_heaviside = SurrogateHeaviside.apply

# SpikingSSM layer, outputting per time step
class SpikingSSM(nn.Module):
	def __init__(self, input_dim, d, m, output_dim, tau=0.9, theta=1.0, epsilon=0.05, sigma=0.1, is_final=False):
		super(SpikingSSM, self).__init__()
		self.d = d
		self.m = m
		self.tau = tau
		self.theta = torch.tensor(theta)
		self.epsilon = epsilon
		self.sigma = sigma
		self.is_final = is_final
		self.output_dim = output_dim  # Store as attribute

		A_bar = torch.randn(m, m)
		eigvals = torch.linalg.eigvals(A_bar)
		max_abs = torch.max(torch.abs(eigvals))
		if max_abs > 1:
			A_bar = A_bar / max_abs  # Normalize for stability
		self.A_bar = nn.Parameter(A_bar)

		self.B_bar = nn.Parameter(torch.randn(m, input_dim))
		self.C = nn.Parameter(torch.randn(d, m))

		self.G = nn.Parameter(torch.ones(d))

		if self.is_final:
			self.W_o = nn.Parameter(torch.randn(output_dim, d))
			self.b_o = nn.Parameter(torch.zeros(output_dim))
			self.P = torch.ones_like(self.W_o)
		else:
			self.W_o = None
			self.b_o = None
			self.P = None

	def heaviside(self, x, theta):
		if self.training:
			return surrogate_heaviside(x, theta)
		else:
			return (x > theta).float()

	def forward(self, X_in):
		batch, n, _ = X_in.shape
		device = X_in.device

		# Precompute transposes for speed
		A_bar_t = self.A_bar.t()
		B_bar_t = self.B_bar.t()
		C_t = self.C.t()
		theta_dev = self.theta.to(device)

		h = torch.zeros(batch, self.m, device=device)
		u = torch.zeros(batch, self.d, device=device)
		outputs = torch.zeros(batch, n, self.d if not self.is_final else self.output_dim, device=device)  # Preallocate

		if self.is_final:
			masked_W_o_t = (self.W_o * self.P.to(device)).t() if self.P is not None else self.W_o.t()

		for t in range(n):
			x_t = X_in[:, t, :]
			h = h @ A_bar_t + x_t @ B_bar_t

			I_t = (h @ C_t) * self.G
			noise = self.epsilon * torch.randn_like(I_t) * self.sigma
			I_prime_t = I_t + noise

			u_prime = self.tau * u + I_prime_t
			s_t = self.heaviside(u_prime, theta_dev)
			u = u_prime * (1 - s_t)

			if self.is_final:
				y_t = s_t @ masked_W_o_t + self.b_o
				outputs[:, t, :] = y_t
			else:
				outputs[:, t, :] = s_t

		return outputs

# LLM wrapper
class SpikingSSM_LLM(nn.Module):
	def __init__(self, vocab_size, d=32, m=16, L=1):  # Reduced for efficiency
		super(SpikingSSM_LLM, self).__init__()
		self.embedding = nn.Embedding(vocab_size, d)
		self.layers = nn.ModuleList()
		for l in range(L):
			input_dim = d
			is_final = (l == L - 1)
			output_dim = vocab_size if is_final else d
			self.layers.append(SpikingSSM(input_dim, d, m, output_dim, is_final=is_final))
		self.vocab_size = vocab_size

	def forward(self, tokens):
		embeds = self.embedding(tokens)
		current_input = embeds
		for layer in self.layers:
			output = layer(current_input)
			if not layer.is_final:
				current_input = output
		return output  # [batch, seq_len, vocab_size]

# Simple tokenizer: split on spaces and punctuation
def tokenize(text):
	return re.findall(r'\w+|[^\w\s]', text)

# Build vocabulary from dataset
def build_vocab(dataset):
	counter = Counter()
	for item in dataset:
		instruction = item['instruction']
		output = item['output']
		counter.update(tokenize(instruction + ' ' + output))
	vocab = {word: idx for idx, (word, _) in enumerate(counter.items(), 2)}
	vocab['<pad>'] = 0
	vocab['<unk>'] = 1
	return vocab

# Custom Dataset class
class ThinkingDataset(Dataset):
	def __init__(self, hf_dataset, vocab, max_length=64):  # Reduced max_length for speed
		self.data = []
		self.vocab = vocab
		self.max_length = max_length
		for item in hf_dataset:
			text = item['instruction'] + ' ' + item['output']
			tokens = [self.vocab.get(t, 1) for t in tokenize(text)]
			tokens = tokens[:max_length] if len(tokens) > max_length else tokens + [0] * (max_length - len(tokens))
			self.data.append(tokens)

	def __len__(self):
		return len(self.data)

	def __getitem__(self, idx):
		seq = torch.tensor(self.data[idx], dtype=torch.long)
		return seq[:-1], seq[1:]

# Training function with mixed precision and optional profiling
def train(model, dataloader, optimizer, device, epochs=3):
	scaler = GradScaler(enabled=(device.type == 'cuda'))
	model.train()

	for epoch in range(epochs):
		total_loss = 0
		s = time.time()
		for index, (inputs, targets) in enumerate(dataloader):
			if index % 100 == 0:
				print(f"Batch {index} / {len(dataloader)} - Epoch {epoch+1}")
				print(f"100 Batches took: {(time.time() - s):.4f} seconds.")
				avg_loss = total_loss / len(dataloader)
				print(f"Average Loss: {avg_loss:.4f}")
				s = time.time()
			inputs, targets = inputs.to(device), targets.to(device)
			optimizer.zero_grad()
			logits = model(inputs)  # [batch, seq_len-1, vocab_size]
			loss = F.cross_entropy(logits.reshape(-1, model.vocab_size), targets.reshape(-1))
			#
			scaler.scale(loss).backward()
			scaler.step(optimizer)
			scaler.update()
			total_loss += loss.item()
		avg_loss = total_loss / len(dataloader)
		print(f"Epoch {epoch+1}, Average Loss: {avg_loss:.4f}")

# Main script
if __name__ == "__main__":
	# Load a small subset of dataset for faster testing
	dataset = load_dataset("HelpingAI/Intermediate-Thinking-130k", split='train')  # Small subset

	# Build vocab from subset
	vocab = build_vocab(dataset)
	vocab_size = len(vocab)
	print(vocab_size)

	# Create custom dataset
	train_dataset = ThinkingDataset(dataset, vocab, max_length=64)
	train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, prefetch_factor=2, num_workers=6)  # Adjusted batch size

	# Initialize model
	device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
	model = SpikingSSM_LLM(vocab_size=vocab_size, d=32, m=16, L=1).to(device)  # Small for basic
	model.compile(mode='reduce-overhead')

	# Optimizer
	optimizer = torch.optim.AdamW(model.parameters(), lr=0.001)

	# Train
	train(model, train_loader, optimizer, device, epochs=3)  # Short for demo
